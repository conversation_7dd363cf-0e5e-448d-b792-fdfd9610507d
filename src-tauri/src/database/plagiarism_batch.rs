use crate::database::{create_surreal_db_index, IndexType};
use crate::database::surreal::get_db;
use crate::models::plagiarism::PlagiarismBatchDb;
use crate::models::surreal::get_update_map;
use chrono::Utc;
use anyhow::Result;

pub struct PlagiarismBatchRepository;

impl PlagiarismBatchRepository {
    /// 创建数据库索引
    pub async fn create_indexes() -> Result<()> {
        let db = get_db();
        create_surreal_db_index(&db, "plagiarism_batch", vec![vec!["batchId"]], IndexType::Unique).await?;
        create_surreal_db_index(&db, "plagiarism_batch", vec![vec!["status"], vec!["createTime"]], IndexType::Normal).await?;
        Ok(())
    }

    /// 保存批次到数据库
    pub async fn save(batch: &mut PlagiarismBatchDb) -> Result<()> {
        let db = get_db();

        // 检查是否已存在
        let existing_record = Self::find_by_batch_id(batch.batch_id).await?;

        if existing_record.is_none() {
            // 如果数据不存在，则创建新记录
            let _: Option<PlagiarismBatchDb> = db.create("plagiarism_batch").content(batch.clone()).await?;
        } else {
            // 更新现有记录
            batch.update_time = Utc::now().timestamp_millis() as u64;
            let value = serde_json::to_value(&batch)?;
            let update_map = get_update_map(value);
            let mut resp = db
                .query("UPDATE plagiarism_batch SET $data WHERE batchId = $batch_id")
                .bind(("data", update_map))
                .bind(("batch_id", batch.batch_id))
                .await?;
            let _: Option<PlagiarismBatchDb> = resp.take(0)?;
        }
        Ok(())
    }

    /// 根据批次ID查找批次
    pub async fn find_by_batch_id(batch_id: u64) -> Result<Option<PlagiarismBatchDb>> {
        let db = get_db();
        let mut resp = db
            .query("SELECT * FROM plagiarism_batch WHERE batchId = $batch_id")
            .bind(("batch_id", batch_id))
            .await?;
        let opt_batch: Option<PlagiarismBatchDb> = resp.take(0)?;
        Ok(opt_batch)
    }

    /// 根据批次ID字符串查找批次
    pub async fn find_by_batch_id_str(batch_id_str: String) -> Result<Option<PlagiarismBatchDb>> {
        // 尝试解析批次ID字符串
        if let Ok(batch_id) = batch_id_str.parse::<u64>() {
            Self::find_by_batch_id(batch_id).await
        } else {
            // 如果不是数字ID，可能是旧格式的字符串ID，直接查询
            let db = get_db();
            let mut resp = db
                .query("SELECT * FROM plagiarism_batch WHERE string::concat('batch_', batchId) = $batch_id_str")
                .bind(("batch_id_str", batch_id_str))
                .await?;
            let opt_batch: Option<PlagiarismBatchDb> = resp.take(0)?;
            Ok(opt_batch)
        }
    }

    /// 分页查询批次列表
    pub async fn find_batches_paginated(
        page_no: i32,
        page_size: i32,
        status: Option<String>,
        keyword: Option<String>,
    ) -> Result<(Vec<PlagiarismBatchDb>, i32)> {
        let db = get_db();

        // 构建查询条件
        let mut where_conditions = Vec::new();

        if let Some(status_val) = status {
            where_conditions.push(format!("status = '{}'", status_val));
        }

        if let Some(keyword_val) = keyword {
            where_conditions.push(format!("(name CONTAINS '{}' OR description CONTAINS '{}')", keyword_val, keyword_val));
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 查询总数
        let count_query = format!("SELECT count() FROM plagiarism_batch {}", where_clause);
        let mut count_resp = db.query(&count_query).await?;
        let total: Option<i32> = count_resp.take(0)?;
        let total_count = total.unwrap_or(0);

        // 分页查询数据
        let offset = (page_no - 1) * page_size;
        let data_query = format!(
            "SELECT * FROM plagiarism_batch {} ORDER BY createTime DESC LIMIT {} START {}",
            where_clause, page_size, offset
        );
        let mut data_resp = db.query(&data_query).await?;
        let batches: Vec<PlagiarismBatchDb> = data_resp.take(0)?;

        Ok((batches, total_count))
    }

    /// 删除批次
    pub async fn delete_by_batch_id(batch_id: u64) -> Result<bool> {
        let db = get_db();
        let mut resp = db
            .query("DELETE FROM plagiarism_batch WHERE batchId = $batch_id")
            .bind(("batch_id", batch_id))
            .await?;
        let result: Option<PlagiarismBatchDb> = resp.take(0)?;
        Ok(result.is_some())
    }

    /// 更新批次状态
    pub async fn update_status(
        batch_id: u64,
        status: String,
        progress: Option<i32>,
    ) -> Result<()> {
        let db = get_db();
        let now = Utc::now().timestamp_millis() as u64;

        let mut update_parts = vec![
            format!("status = '{}'", status),
            format!("updateTime = {}", now),
        ];

        if let Some(p) = progress {
            update_parts.push(format!("progress = {}", p));
        }

        if status == "completed" || status == "failed" {
            update_parts.push(format!("completedTime = {}", now));
        }

        let update_query = format!(
            "UPDATE plagiarism_batch SET {} WHERE batchId = $batch_id",
            update_parts.join(", ")
        );

        let mut resp = db
            .query(&update_query)
            .bind(("batch_id", batch_id))
            .await?;
        let _: Option<PlagiarismBatchDb> = resp.take(0)?;

        Ok(())
    }

    /// 更新批次匹配数量
    pub async fn update_total_matches(
        batch_id: u64,
        total_matches: i32,
    ) -> Result<()> {
        let db = get_db();
        let now = Utc::now().timestamp_millis() as u64;

        let update_query = format!(
            "UPDATE plagiarism_batch SET totalMatches = {}, updateTime = {} WHERE batchId = $batch_id",
            total_matches, now
        );

        let mut resp = db
            .query(&update_query)
            .bind(("batch_id", batch_id))
            .await?;
        let _: Option<PlagiarismBatchDb> = resp.take(0)?;

        Ok(())
    }
}
