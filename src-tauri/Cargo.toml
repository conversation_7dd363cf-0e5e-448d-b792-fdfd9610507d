[package]
name = "book-guard"
version = "1.0.0"
description = "Pre-Publish Book Dup Check App"
authors = ["you"]
license = ""
repository = ""
edition = "2021"

# [lib]
# name = "app_lib"
# crate-type = ["staticlib", "cdylib", "rlib"]
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = ["protocol-asset", "devtools"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
walkdir = "2.5"
zip = "2"
once_cell = "1.21"
chrono = { version = "0.4", features = ["serde"] }
regex = "1.11"
anyhow = "1.0"
serde_derive = "1.0"
lazy_static = { version = "1.5", features = [] }
tokio = { version = "1.45", features = ["full"] }
tokio-tungstenite = "0.26"
log = "0.4"
bincode = { version = "1.3", features = ["i128"] }
surrealdb = { version = "2.3", features = ["kv-rocksdb"] }
tantivy = "0.22"
tantivy-jieba = "0.11"
rand = "0.8"
reqwest = { version = "0.12", features = ["json", "multipart"] }
tauri-plugin-dialog = "2"
tauri-plugin-notification = "2"
tauri-plugin-shell = "2"
tauri-plugin-fs = "2"
tauri-plugin-http = "2"
machine-uid = "0.5"
indexmap = "2.9"
actix = "0.13"
actix-http = "3.9"
actix-web = "4"
actix-files = "0.6"
actix-ws = "0.3"
serde_urlencoded = "0.7"
uuid = { version = "1", features = ["v4"] }
futures-util = "0.3"
tauri-plugin-process = "2.2"
tauri-plugin-opener = "2.2"
tauri-plugin-upload = "2.2"
human-panic = "2.0"
fuzzy-matcher = "0.3"
similar = "2.7"
strsim = "0.11"
snowflaker = "0.3.6"
[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

#[profile.dev]
#incremental = true # Compile your binary in smaller steps.
#codegen-units = 32 # Default is 16, but you can tweak this for your use case
#
#[profile.release]
#incremental = true
#codegen-units = 1  # Allows LLVM to perform better optimization.
#lto = true         # Enables link-time-optimizations.
#opt-level = "s"    # Prioritizes small binary size. Use `3` if you prefer speed.
#panic = "abort"    # Higher performance by disabling panic handlers.
#strip = true       # Ensures debug symbols are removed.
